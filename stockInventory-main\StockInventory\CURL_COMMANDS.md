# 🚀 cURL Commands - Employee Attendance APIs

## 🔐 Authentication

### **1. <PERSON>gin (Get JWT Token)**
```bash
curl -X POST http://localhost:8080/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123"
  }'
```

**Save the token from response and use it in subsequent requests**

---

## 👥 Employee Management

### **Create Employee**
```bash
curl -X POST http://localhost:8080/api/employees \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "username": "john.doe",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "role": ["user"]
  }'
```

### **Get All Employees**
```bash
curl -X GET http://localhost:8080/api/employees \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Search Employee by Name**
```bash
curl -X GET "http://localhost:8080/api/employees/search?name=John" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 📅 Attendance Management

### **🎯 Get Attendance List (Main View)**
```bash
curl -X GET http://localhost:8080/api/attendance/list \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Today's Attendance**
```bash
curl -X GET http://localhost:8080/api/attendance/today \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Attendance for Specific Date**
```bash
curl -X GET "http://localhost:8080/api/attendance/list?date=2024-01-15" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Mark Attendance (Admin/Moderator)**
```bash
curl -X POST http://localhost:8080/api/attendance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "employeeId": 1,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:00:00",
    "checkOutTime": "17:30:00",
    "status": "PRESENT",
    "remarks": "Regular working day"
  }'
```

### **Employee Self Check-In**
```bash
curl -X POST http://localhost:8080/api/attendance/checkin \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Employee Self Check-Out**
```bash
curl -X POST http://localhost:8080/api/attendance/checkout \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Employee Attendance History**
```bash
curl -X GET http://localhost:8080/api/attendance/employee/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Attendance by Status**
```bash
curl -X GET http://localhost:8080/api/attendance/status/PRESENT \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Attendance by Date Range**
```bash
curl -X GET "http://localhost:8080/api/attendance/date-range?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Update Attendance**
```bash
curl -X PUT http://localhost:8080/api/attendance/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "employeeId": 1,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:15:00",
    "checkOutTime": "17:45:00",
    "status": "LATE",
    "remarks": "Arrived 15 minutes late"
  }'
```

### **Delete Attendance Record**
```bash
curl -X DELETE http://localhost:8080/api/attendance/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🎯 Quick Test Workflow

### **1. Complete Employee & Attendance Setup**
```bash
# 1. Login
TOKEN=$(curl -s -X POST http://localhost:8080/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}' | \
  jq -r '.token')

# 2. Create Employee
curl -X POST http://localhost:8080/api/employees \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "username": "john.doe",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "role": ["user"]
  }'

# 3. Mark Attendance
curl -X POST http://localhost:8080/api/attendance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "employeeId": 1,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:00:00",
    "checkOutTime": "17:30:00",
    "status": "PRESENT",
    "remarks": "Regular working day"
  }'

# 4. View Attendance List
curl -X GET http://localhost:8080/api/attendance/list \
  -H "Authorization: Bearer $TOKEN"
```

---

## 📊 Sample Responses

### **Attendance List Response:**
```json
[
  {
    "empId": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["ROLE_USER"],
    "attendanceDate": "2024-01-15",
    "inTime": "09:00:00",
    "outTime": "17:30:00",
    "status": "PRESENT",
    "totalHours": 8.5,
    "remarks": "Regular working day"
  }
]
```

### **Employee List Response:**
```json
[
  {
    "id": 1,
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "roles": ["ROLE_USER"]
  }
]
```

---

## 🔧 Status Values for Testing

Use these exact values for the `status` field:
- `PRESENT`
- `ABSENT`
- `LATE`
- `HALF_DAY`
- `SICK_LEAVE`
- `CASUAL_LEAVE`
- `WORK_FROM_HOME`
- `HOLIDAY`
- `WEEKEND`

---

## 📝 Notes

1. **Replace `YOUR_JWT_TOKEN`** with actual token from login response
2. **Date Format:** Use `YYYY-MM-DD` (e.g., `2024-01-15`)
3. **Time Format:** Use `HH:MM:SS` (e.g., `09:00:00`)
4. **Employee IDs:** Use actual employee IDs from your system
5. **Base URL:** Change `localhost:8080` if your server runs on different port

**Ready for API testing! 🚀**
