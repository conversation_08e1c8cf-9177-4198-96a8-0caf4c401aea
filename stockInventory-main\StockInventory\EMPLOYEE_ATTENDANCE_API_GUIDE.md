# 👥 Employee Attendance Management System - API Guide

## 🚀 Overview

The Employee Attendance Management System provides comprehensive functionality to manage employees and track their daily attendance with detailed reporting capabilities.

## 📊 Features

### **Employee Management**
- ✅ Create, Read, Update, Delete employees
- ✅ Employee ID, First Name, Last Name, Role management
- ✅ Search employees by name
- ✅ Role-based access control

### **Attendance Tracking**
- ✅ Daily attendance marking with In/Out times
- ✅ Multiple attendance statuses (Present, Absent, Late, etc.)
- ✅ Self check-in/check-out functionality
- ✅ Automatic total hours calculation
- ✅ Overtime tracking
- ✅ Attendance history and reporting

## 🔐 Authentication Required

All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## 👥 Employee Management APIs

### **1. Get All Employees**
```http
GET /api/employees
```
**Access:** USER+ (USER/MODERATOR/ADMIN)

**Response:**
```json
[
  {
    "id": 1,
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "roles": ["ROLE_USER"]
  }
]
```

### **2. Get Employee by ID**
```http
GET /api/employees/{id}
```
**Access:** USER+

### **3. Get Employee by Employee ID**
```http
GET /api/employees/empid/{employeeId}
```
**Access:** USER+

### **4. Search Employees by Name**
```http
GET /api/employees/search?name={searchTerm}
```
**Access:** USER+

### **5. Create Employee**
```http
POST /api/employees
```
**Access:** MODERATOR+

**Request Body:**
```json
{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "employeeId": "EMP002",
  "role": ["user"]
}
```

### **6. Update Employee**
```http
PUT /api/employees/{id}
```
**Access:** MODERATOR+

### **7. Delete Employee**
```http
DELETE /api/employees/{id}
```
**Access:** ADMIN

---

## 📅 Attendance Management APIs

### **1. Get All Attendance Records**
```http
GET /api/attendance
```
**Access:** USER+

### **2. Get Attendance List (Formatted)**
```http
GET /api/attendance/list?date=2024-01-15
```
**Access:** USER+

**Response:**
```json
[
  {
    "empId": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["ROLE_USER"],
    "attendanceDate": "2024-01-15",
    "inTime": "09:00:00",
    "outTime": "17:30:00",
    "status": "PRESENT",
    "totalHours": 8.5,
    "remarks": "On time"
  }
]
```

### **3. Get Today's Attendance**
```http
GET /api/attendance/today
```
**Access:** USER+

### **4. Get Employee Attendance History**
```http
GET /api/attendance/employee/{employeeId}
```
**Access:** USER+

### **5. Get Attendance by Date Range**
```http
GET /api/attendance/date-range?startDate=2024-01-01&endDate=2024-01-31
```
**Access:** USER+

### **6. Get Attendance by Status**
```http
GET /api/attendance/status/{status}
```
**Access:** USER+

**Available Statuses:**
- `PRESENT`
- `ABSENT`
- `LATE`
- `HALF_DAY`
- `SICK_LEAVE`
- `CASUAL_LEAVE`
- `WORK_FROM_HOME`
- `HOLIDAY`
- `WEEKEND`

### **7. Mark Attendance (Admin/Moderator)**
```http
POST /api/attendance
```
**Access:** MODERATOR+

**Request Body:**
```json
{
  "employeeId": 1,
  "attendanceDate": "2024-01-15",
  "checkInTime": "09:00:00",
  "checkOutTime": "17:30:00",
  "status": "PRESENT",
  "remarks": "Regular working day"
}
```

### **8. Self Check-In**
```http
POST /api/attendance/checkin
```
**Access:** USER+ (Employee can check themselves in)

### **9. Self Check-Out**
```http
POST /api/attendance/checkout
```
**Access:** USER+ (Employee can check themselves out)

### **10. Update Attendance**
```http
PUT /api/attendance/{id}
```
**Access:** MODERATOR+

### **11. Delete Attendance Record**
```http
DELETE /api/attendance/{id}
```
**Access:** ADMIN

---

## 📋 Attendance Status Types

| Status | Description |
|--------|-------------|
| `PRESENT` | Employee is present and working |
| `ABSENT` | Employee is absent without leave |
| `LATE` | Employee arrived late |
| `HALF_DAY` | Employee worked half day |
| `SICK_LEAVE` | Employee on sick leave |
| `CASUAL_LEAVE` | Employee on casual leave |
| `WORK_FROM_HOME` | Employee working from home |
| `HOLIDAY` | Public holiday |
| `WEEKEND` | Weekend day |

---

## 🔧 Key Features

### **Automatic Calculations**
- **Total Hours:** Automatically calculated from check-in and check-out times
- **Overtime:** Hours beyond 8-hour standard work day
- **Unique Constraints:** One attendance record per employee per day

### **Self-Service Features**
- Employees can check themselves in/out
- Real-time attendance tracking
- Personal attendance history access

### **Administrative Features**
- Bulk attendance management
- Attendance reporting and analytics
- Role-based access control
- Audit trail with "marked by" tracking

---

## 🎯 Quick Start Examples

### **1. Create an Employee**
```bash
curl -X POST http://localhost:8080/api/employees \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "alice.johnson",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Alice",
    "lastName": "Johnson",
    "employeeId": "EMP003",
    "role": ["user"]
  }'
```

### **2. Employee Self Check-In**
```bash
curl -X POST http://localhost:8080/api/attendance/checkin \
  -H "Authorization: Bearer <employee-token>"
```

### **3. Get Today's Attendance List**
```bash
curl -X GET http://localhost:8080/api/attendance/today \
  -H "Authorization: Bearer <token>"
```

### **4. Mark Attendance for Employee**
```bash
curl -X POST http://localhost:8080/api/attendance \
  -H "Authorization: Bearer <moderator-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "employeeId": 1,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:15:00",
    "checkOutTime": "17:45:00",
    "status": "LATE",
    "remarks": "Arrived 15 minutes late"
  }'
```

---

## 🎉 System Ready!

Your Employee Attendance Management System is now fully integrated and ready for use! 

### **Database Tables Created:**
- ✅ `attendance` - Stores daily attendance records
- ✅ `users` table updated with employee fields (firstName, lastName, employeeId)

### **Next Steps:**
1. Start the application: `mvn exec:java -Dexec.mainClass="com.example.stockinventorysystem.StockInventorySystemApplication"`
2. Create employee accounts using the Employee APIs
3. Start tracking attendance using the Attendance APIs
4. Build your frontend to consume these APIs

**Happy tracking! 📊**
