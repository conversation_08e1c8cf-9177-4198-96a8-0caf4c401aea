{"info": {"name": "Invoice API - Simple String Numbers", "description": "Invoice API collection with simple string number IDs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/signin", "host": ["{{baseUrl}}"], "path": ["api", "auth", "signin"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.token);", "    console.log('✅ Login successful, token saved');", "}"]}}]}]}, {"name": "Invoice Management", "item": [{"name": "Create Invoice - ID: 10001", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceId\": \"10001\",\n  \"clientName\": \"Tech Solutions Inc\",\n  \"amount\": 3500.00,\n  \"invoiceDate\": \"2024-01-15\",\n  \"description\": \"Software development services for mobile application project\",\n  \"paymentStatus\": \"DRAFT\"\n}"}, "url": {"raw": "{{baseUrl}}/api/invoices", "host": ["{{baseUrl}}"], "path": ["api", "invoices"]}}}, {"name": "Create Invoice - ID: 10002", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceId\": \"10002\",\n  \"clientName\": \"Digital Marketing Corp\",\n  \"amount\": 2750.50,\n  \"invoiceDate\": \"2024-01-16\",\n  \"description\": \"Website redesign and SEO optimization services\",\n  \"paymentStatus\": \"DRAFT\"\n}"}, "url": {"raw": "{{baseUrl}}/api/invoices", "host": ["{{baseUrl}}"], "path": ["api", "invoices"]}}}, {"name": "Create Invoice - ID: 10003", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceId\": \"10003\",\n  \"clientName\": \"Global Consulting Ltd\",\n  \"amount\": 5200.00,\n  \"invoiceDate\": \"2024-01-17\",\n  \"description\": \"Business process analysis and improvement consultation\",\n  \"paymentStatus\": \"DRAFT\"\n}"}, "url": {"raw": "{{baseUrl}}/api/invoices", "host": ["{{baseUrl}}"], "path": ["api", "invoices"]}}}, {"name": "Get All Invoices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/invoices", "host": ["{{baseUrl}}"], "path": ["api", "invoices"]}}}, {"name": "Get Invoice by Invoice ID - 10001", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/invoices/invoice/10001", "host": ["{{baseUrl}}"], "path": ["api", "invoices", "invoice", "10001"]}}}, {"name": "Update Invoice - ID: 10001", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceId\": \"10001\",\n  \"clientName\": \"Tech Solutions Inc\",\n  \"amount\": 4000.00,\n  \"invoiceDate\": \"2024-01-15\",\n  \"description\": \"Updated: Software development services for mobile application project with additional features\",\n  \"paymentStatus\": \"PENDING\"\n}"}, "url": {"raw": "{{baseUrl}}/api/invoices/1", "host": ["{{baseUrl}}"], "path": ["api", "invoices", "1"]}}}, {"name": "Update Payment Status - PENDING", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/invoices/1/status?status=PENDING", "host": ["{{baseUrl}}"], "path": ["api", "invoices", "1", "status"], "query": [{"key": "status", "value": "PENDING"}]}}}, {"name": "Approve Invoice", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/invoices/1/approve", "host": ["{{baseUrl}}"], "path": ["api", "invoices", "1", "approve"]}}}, {"name": "Get Invoices by Status - APPROVED", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/invoices/status/APPROVED", "host": ["{{baseUrl}}"], "path": ["api", "invoices", "status", "APPROVED"]}}}]}]}