# 📋 Postman API Examples - Employee Attendance Management

## 🚀 Quick Setup Guide

### **1. Import Collection**
1. Open Postman
2. Click "Import" 
3. Select `Postman_Employee_Attendance_APIs.json`
4. Collection will be imported with all endpoints

### **2. Set Environment Variables**
Create a new environment in Postman with these variables:
- `base_url`: `http://localhost:8080`
- `jwt_token`: `your_jwt_token_here` (will be set after login)

---

## 🔐 Authentication Flow

### **Step 1: Login to Get JWT Token**
```http
POST {{base_url}}/api/auth/signin
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzUxMiJ9...",
  "type": "Bearer",
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "roles": ["ROLE_ADMIN"]
}
```

**⚠️ Important:** Copy the `token` value and set it as `jwt_token` in your environment variables.

---

## 👥 Employee Management APIs

### **Create Employee**
```http
POST {{base_url}}/api/employees
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "username": "john.doe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "employeeId": "EMP001",
  "role": ["user"]
}
```

### **Get All Employees**
```http
GET {{base_url}}/api/employees
Authorization: Bearer {{jwt_token}}
```

**Response:**
```json
[
  {
    "id": 1,
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "roles": ["ROLE_USER"]
  }
]
```

---

## 📅 Attendance Management APIs

### **🎯 Main Attendance List (Your Requested View)**
```http
GET {{base_url}}/api/attendance/list
Authorization: Bearer {{jwt_token}}
```

**Response (Exactly what you requested):**
```json
[
  {
    "empId": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["ROLE_USER"],
    "attendanceDate": "2024-01-15",
    "inTime": "09:00:00",
    "outTime": "17:30:00",
    "status": "PRESENT",
    "totalHours": 8.5,
    "remarks": "On time"
  },
  {
    "empId": "EMP002",
    "firstName": "Jane",
    "lastName": "Smith",
    "roles": ["ROLE_USER"],
    "attendanceDate": "2024-01-15",
    "inTime": "09:15:00",
    "outTime": "17:45:00",
    "status": "LATE",
    "totalHours": 8.5,
    "remarks": "Arrived 15 minutes late"
  }
]
```

### **Get Attendance for Specific Date**
```http
GET {{base_url}}/api/attendance/list?date=2024-01-15
Authorization: Bearer {{jwt_token}}
```

### **Mark Attendance (Admin/Moderator)**
```http
POST {{base_url}}/api/attendance
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "employeeId": 1,
  "attendanceDate": "2024-01-15",
  "checkInTime": "09:00:00",
  "checkOutTime": "17:30:00",
  "status": "PRESENT",
  "remarks": "Regular working day"
}
```

### **Employee Self Check-In**
```http
POST {{base_url}}/api/attendance/checkin
Authorization: Bearer {{jwt_token}}
```

**Response:**
```json
{
  "message": "Checked in successfully!"
}
```

### **Employee Self Check-Out**
```http
POST {{base_url}}/api/attendance/checkout
Authorization: Bearer {{jwt_token}}
```

### **Get Employee Attendance History**
```http
GET {{base_url}}/api/attendance/employee/1
Authorization: Bearer {{jwt_token}}
```

### **Get Attendance by Status**
```http
GET {{base_url}}/api/attendance/status/PRESENT
Authorization: Bearer {{jwt_token}}
```

**Available Status Values:**
- `PRESENT`
- `ABSENT`
- `LATE`
- `HALF_DAY`
- `SICK_LEAVE`
- `CASUAL_LEAVE`
- `WORK_FROM_HOME`
- `HOLIDAY`
- `WEEKEND`

### **Get Attendance by Date Range**
```http
GET {{base_url}}/api/attendance/date-range?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{jwt_token}}
```

### **Update Attendance**
```http
PUT {{base_url}}/api/attendance/1
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "employeeId": 1,
  "attendanceDate": "2024-01-15",
  "checkInTime": "09:15:00",
  "checkOutTime": "17:45:00",
  "status": "LATE",
  "remarks": "Arrived 15 minutes late"
}
```

---

## 🎯 Common Use Cases

### **1. Daily Attendance Workflow**
1. **Morning:** Employee checks in
   ```http
   POST {{base_url}}/api/attendance/checkin
   ```

2. **Evening:** Employee checks out
   ```http
   POST {{base_url}}/api/attendance/checkout
   ```

3. **Admin:** View today's attendance
   ```http
   GET {{base_url}}/api/attendance/today
   ```

### **2. Admin Attendance Management**
1. **Create Employee:**
   ```http
   POST {{base_url}}/api/employees
   ```

2. **Mark Attendance for Employee:**
   ```http
   POST {{base_url}}/api/attendance
   ```

3. **View Attendance List:**
   ```http
   GET {{base_url}}/api/attendance/list
   ```

### **3. Reporting Workflow**
1. **Monthly Report:**
   ```http
   GET {{base_url}}/api/attendance/date-range?startDate=2024-01-01&endDate=2024-01-31
   ```

2. **Present Employees Today:**
   ```http
   GET {{base_url}}/api/attendance/status/PRESENT
   ```

3. **Employee History:**
   ```http
   GET {{base_url}}/api/attendance/employee/1
   ```

---

## 🔧 Testing Tips

### **1. Role-Based Testing**
- **USER Role:** Can check-in/out, view own attendance
- **MODERATOR Role:** Can manage attendance for all employees
- **ADMIN Role:** Full access to all operations

### **2. Error Scenarios to Test**
- Duplicate check-in on same day
- Check-out without check-in
- Invalid employee ID
- Invalid date formats
- Missing required fields

### **3. Data Validation**
- Date format: `YYYY-MM-DD`
- Time format: `HH:MM:SS`
- Status values: Use exact enum values
- Employee ID: Must exist in system

---

## 📊 Sample Test Data

### **Create Test Employees:**
```json
[
  {
    "username": "john.doe",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "EMP001",
    "role": ["user"]
  },
  {
    "username": "jane.smith",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Jane",
    "lastName": "Smith",
    "employeeId": "EMP002",
    "role": ["user"]
  }
]
```

### **Sample Attendance Records:**
```json
[
  {
    "employeeId": 1,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:00:00",
    "checkOutTime": "17:30:00",
    "status": "PRESENT",
    "remarks": "On time"
  },
  {
    "employeeId": 2,
    "attendanceDate": "2024-01-15",
    "checkInTime": "09:15:00",
    "checkOutTime": "17:45:00",
    "status": "LATE",
    "remarks": "Traffic delay"
  }
]
```

---

## 🎉 Ready to Test!

1. **Import** the Postman collection
2. **Set** environment variables
3. **Login** to get JWT token
4. **Create** test employees
5. **Test** attendance APIs
6. **View** the attendance list

**Your attendance system is ready for comprehensive testing! 🚀**
