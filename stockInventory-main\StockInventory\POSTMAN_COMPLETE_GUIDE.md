# 📋 Complete Postman Guide - Employee Attendance APIs

## 🎯 What You Get

I've created a complete Postman API collection for your Employee Attendance Management System with:

✅ **25+ API endpoints** for employee and attendance management  
✅ **Pre-configured authentication** with JWT tokens  
✅ **Sample requests and responses** for all operations  
✅ **Environment variables** for easy testing  
✅ **Complete documentation** with examples  

---

## 📁 Files Created

### **1. Postman Collection**
- **File:** `Postman_Employee_Attendance_APIs.json`
- **Contains:** All API endpoints organized in folders
- **Import:** Directly into Postman

### **2. Environment File**
- **File:** `Postman_Environment.json`
- **Contains:** Pre-configured variables (base_url, jwt_token, etc.)
- **Import:** Into Postman environments

### **3. Documentation Files**
- **`POSTMAN_API_EXAMPLES.md`** - Detailed API examples with responses
- **`CURL_COMMANDS.md`** - Command-line alternatives
- **`POSTMAN_COMPLETE_GUIDE.md`** - This comprehensive guide

---

## 🚀 Quick Setup (3 Steps)

### **Step 1: Import Collection**
1. Open Postman
2. Click **"Import"** button
3. Select `Postman_Employee_Attendance_APIs.json`
4. Collection imported with 25+ endpoints!

### **Step 2: Import Environment**
1. Click **"Environments"** in Postman
2. Click **"Import"**
3. Select `Postman_Environment.json`
4. Set as active environment

### **Step 3: Get JWT Token**
1. Run **"Authentication > Login"** request
2. Copy the `token` from response
3. Set it as `jwt_token` in environment
4. Ready to test all APIs!

---

## 📊 API Collection Structure

```
📁 Employee Attendance Management APIs
├── 🔐 Authentication
│   ├── Login
│   └── Register User
├── 👥 Employee Management
│   ├── Get All Employees
│   ├── Get Employee by ID
│   ├── Get Employee by Employee ID
│   ├── Search Employees by Name
│   ├── Create Employee
│   ├── Update Employee
│   └── Delete Employee
└── 📅 Attendance Management
    ├── Get Attendance List (Main View) ⭐
    ├── Get Attendance List by Date
    ├── Get Today's Attendance
    ├── Get All Attendance Records
    ├── Get Employee Attendance History
    ├── Get Attendance by Date Range
    ├── Get Attendance by Status
    ├── Mark Attendance (Admin/Moderator)
    ├── Employee Self Check-In
    ├── Employee Self Check-Out
    ├── Update Attendance
    └── Delete Attendance Record
```

---

## 🎯 Key Endpoints for Your Attendance List

### **Main Attendance List (Your Requested View)**
```
GET {{base_url}}/api/attendance/list
```
**Returns exactly what you asked for:**
- Emp ID ✅
- First Name ✅
- Last Name ✅
- Role ✅
- Attendance Date ✅
- In & Out Times ✅
- Status ✅

### **Sample Response:**
```json
[
  {
    "empId": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["ROLE_USER"],
    "attendanceDate": "2024-01-15",
    "inTime": "09:00:00",
    "outTime": "17:30:00",
    "status": "PRESENT",
    "totalHours": 8.5,
    "remarks": "On time"
  }
]
```

---

## 🔧 Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | Server URL | `http://localhost:8080` |
| `jwt_token` | Authentication token | `eyJhbGciOiJIUzUxMiJ9...` |
| `admin_username` | Admin login | `admin` |
| `admin_password` | Admin password | `password123` |
| `test_employee_id` | Test employee ID | `1` |
| `test_emp_code` | Test employee code | `EMP001` |
| `current_date` | Current date | `2024-01-15` |
| `start_date` | Range start | `2024-01-01` |
| `end_date` | Range end | `2024-01-31` |

---

## 📝 Testing Workflow

### **1. Authentication Flow**
```
1. Login → Get JWT Token
2. Set token in environment
3. All subsequent requests authenticated
```

### **2. Employee Setup**
```
1. Create Employee → POST /api/employees
2. Verify Creation → GET /api/employees
3. Search Employee → GET /api/employees/search?name=John
```

### **3. Attendance Management**
```
1. Mark Attendance → POST /api/attendance
2. View Attendance List → GET /api/attendance/list
3. Employee Check-in → POST /api/attendance/checkin
4. Employee Check-out → POST /api/attendance/checkout
```

### **4. Reporting**
```
1. Today's Report → GET /api/attendance/today
2. Date Range Report → GET /api/attendance/date-range
3. Status Report → GET /api/attendance/status/PRESENT
4. Employee History → GET /api/attendance/employee/1
```

---

## 🎯 Common Test Scenarios

### **Scenario 1: Daily Attendance**
1. **Employee Check-in** (Morning)
2. **Employee Check-out** (Evening)
3. **View Today's Attendance** (Admin)

### **Scenario 2: Admin Management**
1. **Create New Employee**
2. **Mark Attendance for Employee**
3. **View Attendance List**
4. **Update Attendance if needed**

### **Scenario 3: Reporting**
1. **Monthly Attendance Report**
2. **Present Employees Today**
3. **Late Arrivals Report**
4. **Individual Employee History**

---

## 🔍 Attendance Status Values

Use these exact values in your requests:
- `PRESENT` - Employee present and working
- `ABSENT` - Employee absent
- `LATE` - Employee arrived late
- `HALF_DAY` - Half day work
- `SICK_LEAVE` - On sick leave
- `CASUAL_LEAVE` - On casual leave
- `WORK_FROM_HOME` - Working remotely
- `HOLIDAY` - Public holiday
- `WEEKEND` - Weekend day

---

## 🚨 Important Notes

### **Authentication**
- All APIs require JWT token except login/register
- Token expires after certain time - re-login if needed
- Use Bearer token format: `Bearer your_jwt_token`

### **Date/Time Formats**
- **Date:** `YYYY-MM-DD` (e.g., `2024-01-15`)
- **Time:** `HH:MM:SS` (e.g., `09:00:00`)

### **Role-Based Access**
- **USER:** Can check-in/out, view own attendance
- **MODERATOR:** Can manage attendance for all employees
- **ADMIN:** Full access to all operations

### **Error Handling**
- Check response status codes
- Read error messages in response body
- Common errors: 401 (Unauthorized), 400 (Bad Request), 404 (Not Found)

---

## 🎉 You're All Set!

### **What You Can Do Now:**
1. ✅ **Import** Postman collection and environment
2. ✅ **Login** to get JWT token
3. ✅ **Create** employees with all details
4. ✅ **Track** daily attendance with in/out times
5. ✅ **View** attendance list with all requested fields
6. ✅ **Generate** reports by date, status, employee
7. ✅ **Manage** attendance with full CRUD operations

### **Your Attendance List Includes:**
- ✅ **Emp ID** - Employee identification
- ✅ **First Name** - Employee first name
- ✅ **Last Name** - Employee last name  
- ✅ **Role** - Employee roles/permissions
- ✅ **Attendance** - Date of attendance
- ✅ **In & Out** - Check-in and check-out times
- ✅ **Status** - Attendance status (Present/Absent/Late/etc.)

**Start testing your Employee Attendance Management System now! 🚀📊**
