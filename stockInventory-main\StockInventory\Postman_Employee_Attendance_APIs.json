{"info": {"_postman_id": "attendance-management-apis", "name": "Employee Attendance Management APIs", "description": "Complete API collection for Employee and Attendance Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/signin", "host": ["{{base_url}}"], "path": ["api", "auth", "signin"]}}, "response": []}, {"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"role\": [\"user\"]\n}"}, "url": {"raw": "{{base_url}}/api/auth/signup", "host": ["{{base_url}}"], "path": ["api", "auth", "signup"]}}, "response": []}]}, {"name": "Employee Management", "item": [{"name": "Get All Employees", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}, "response": []}, {"name": "Get Employee by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/employees/1", "host": ["{{base_url}}"], "path": ["api", "employees", "1"]}}, "response": []}, {"name": "Get Employee by Employee ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/employees/empid/EMP001", "host": ["{{base_url}}"], "path": ["api", "employees", "empid", "EMP001"]}}, "response": []}, {"name": "Search Employees by Name", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/employees/search?name=John", "host": ["{{base_url}}"], "path": ["api", "employees", "search"], "query": [{"key": "name", "value": "<PERSON>"}]}}, "response": []}, {"name": "Create Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"john.doe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"employeeId\": \"EMP001\",\n  \"role\": [\"user\"]\n}"}, "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}, "response": []}, {"name": "Update Employee", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"john.doe.updated\",\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"employeeId\": \"EMP001\",\n  \"role\": [\"user\", \"mod\"]\n}"}, "url": {"raw": "{{base_url}}/api/employees/1", "host": ["{{base_url}}"], "path": ["api", "employees", "1"]}}, "response": []}, {"name": "Delete Employee", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/employees/1", "host": ["{{base_url}}"], "path": ["api", "employees", "1"]}}, "response": []}]}, {"name": "Attendance Management", "item": [{"name": "Get Attendance List (Main View)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/list", "host": ["{{base_url}}"], "path": ["api", "attendance", "list"]}}, "response": []}, {"name": "Get Attendance List by Date", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/list?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "attendance", "list"], "query": [{"key": "date", "value": "2024-01-15"}]}}, "response": []}, {"name": "Get Today's Attendance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/today", "host": ["{{base_url}}"], "path": ["api", "attendance", "today"]}}, "response": []}, {"name": "Get All Attendance Records", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance", "host": ["{{base_url}}"], "path": ["api", "attendance"]}}, "response": []}, {"name": "Get Employee Attendance History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/employee/1", "host": ["{{base_url}}"], "path": ["api", "attendance", "employee", "1"]}}, "response": []}, {"name": "Get Attendance by Date Range", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/date-range?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "attendance", "date-range"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}}, "response": []}, {"name": "Get Attendance by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/status/PRESENT", "host": ["{{base_url}}"], "path": ["api", "attendance", "status", "PRESENT"]}}, "response": []}, {"name": "Mark Attendance (Admin/Moderator)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"employeeId\": 1,\n  \"attendanceDate\": \"2024-01-15\",\n  \"checkInTime\": \"09:00:00\",\n  \"checkOutTime\": \"17:30:00\",\n  \"status\": \"PRESENT\",\n  \"remarks\": \"Regular working day\"\n}"}, "url": {"raw": "{{base_url}}/api/attendance", "host": ["{{base_url}}"], "path": ["api", "attendance"]}}, "response": []}, {"name": "Employee Self Check-In", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/checkin", "host": ["{{base_url}}"], "path": ["api", "attendance", "checkin"]}}, "response": []}, {"name": "Employee Self Check-Out", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/checkout", "host": ["{{base_url}}"], "path": ["api", "attendance", "checkout"]}}, "response": []}, {"name": "Update Attendance", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"employeeId\": 1,\n  \"attendanceDate\": \"2024-01-15\",\n  \"checkInTime\": \"09:15:00\",\n  \"checkOutTime\": \"17:45:00\",\n  \"status\": \"LATE\",\n  \"remarks\": \"Arrived 15 minutes late\"\n}"}, "url": {"raw": "{{base_url}}/api/attendance/1", "host": ["{{base_url}}"], "path": ["api", "attendance", "1"]}}, "response": []}, {"name": "Delete Attendance Record", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/attendance/1", "host": ["{{base_url}}"], "path": ["api", "attendance", "1"]}}, "response": []}]}]}