package com.example.stockinventorysystem.controller;

import com.example.stockinventorysystem.dto.AttendanceListResponse;
import com.example.stockinventorysystem.dto.AttendanceRequest;
import com.example.stockinventorysystem.dto.AttendanceResponse;
import com.example.stockinventorysystem.dto.MessageResponse;
import com.example.stockinventorysystem.model.Attendance;
import com.example.stockinventorysystem.model.EAttendanceStatus;
import com.example.stockinventorysystem.model.User;
import com.example.stockinventorysystem.repository.AttendanceRepository;
import com.example.stockinventorysystem.repository.UserRepository;
import com.example.stockinventorysystem.security.services.UserDetailsImpl;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/attendance")
public class AttendanceController {

    @Autowired
    AttendanceRepository attendanceRepository;

    @Autowired
    UserRepository userRepository;

    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceResponse>> getAllAttendance() {
        List<Attendance> attendanceList = attendanceRepository.findAll();
        List<AttendanceResponse> attendanceResponses = attendanceList.stream()
                .map(this::convertToAttendanceResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceResponses);
    }

    @GetMapping("/list")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceListResponse>> getAttendanceList(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        LocalDate targetDate = date != null ? date : LocalDate.now();
        List<Attendance> attendanceList = attendanceRepository.findTodaysAttendance(targetDate);
        
        List<AttendanceListResponse> attendanceListResponses = attendanceList.stream()
                .map(this::convertToAttendanceListResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceListResponses);
    }

    @GetMapping("/today")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceListResponse>> getTodaysAttendance() {
        List<Attendance> attendanceList = attendanceRepository.findTodaysAttendance(LocalDate.now());
        List<AttendanceListResponse> attendanceListResponses = attendanceList.stream()
                .map(this::convertToAttendanceListResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceListResponses);
    }

    @GetMapping("/employee/{employeeId}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceResponse>> getEmployeeAttendance(@PathVariable Long employeeId) {
        Optional<User> employeeOpt = userRepository.findById(employeeId);
        if (!employeeOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }

        List<Attendance> attendanceList = attendanceRepository.findByEmployeeOrderByAttendanceDateDesc(employeeOpt.get());
        List<AttendanceResponse> attendanceResponses = attendanceList.stream()
                .map(this::convertToAttendanceResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceResponses);
    }

    @GetMapping("/date-range")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceResponse>> getAttendanceByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<Attendance> attendanceList = attendanceRepository.findByAttendanceDateBetween(startDate, endDate);
        List<AttendanceResponse> attendanceResponses = attendanceList.stream()
                .map(this::convertToAttendanceResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceResponses);
    }

    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<AttendanceResponse>> getAttendanceByStatus(@PathVariable EAttendanceStatus status) {
        List<Attendance> attendanceList = attendanceRepository.findByStatus(status);
        List<AttendanceResponse> attendanceResponses = attendanceList.stream()
                .map(this::convertToAttendanceResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(attendanceResponses);
    }

    @PostMapping
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> markAttendance(@Valid @RequestBody AttendanceRequest attendanceRequest, 
                                          Authentication authentication) {
        Optional<User> employeeOpt = userRepository.findById(attendanceRequest.getEmployeeId());
        if (!employeeOpt.isPresent()) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Employee not found!"));
        }

        User employee = employeeOpt.get();

        // Check if attendance already exists for this date
        if (attendanceRepository.existsByEmployeeAndAttendanceDate(employee, attendanceRequest.getAttendanceDate())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Attendance already marked for this date!"));
        }

        // Get current user (who is marking the attendance)
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Optional<User> markedByOpt = userRepository.findById(userDetails.getId());

        Attendance attendance = new Attendance();
        attendance.setEmployee(employee);
        attendance.setAttendanceDate(attendanceRequest.getAttendanceDate());
        attendance.setCheckInTime(attendanceRequest.getCheckInTime());
        attendance.setCheckOutTime(attendanceRequest.getCheckOutTime());
        attendance.setStatus(attendanceRequest.getStatus());
        attendance.setRemarks(attendanceRequest.getRemarks());
        
        if (markedByOpt.isPresent()) {
            attendance.setMarkedBy(markedByOpt.get());
        }

        attendanceRepository.save(attendance);

        return ResponseEntity.ok(new MessageResponse("Attendance marked successfully!"));
    }

    @PostMapping("/checkin")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> checkIn(Authentication authentication) {
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Optional<User> employeeOpt = userRepository.findById(userDetails.getId());
        
        if (!employeeOpt.isPresent() || employeeOpt.get().getEmployeeId() == null) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Employee profile not found!"));
        }

        User employee = employeeOpt.get();
        LocalDate today = LocalDate.now();

        // Check if already checked in today
        Optional<Attendance> existingAttendance = attendanceRepository.findByEmployeeAndAttendanceDate(employee, today);
        if (existingAttendance.isPresent()) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Already checked in today!"));
        }

        Attendance attendance = new Attendance();
        attendance.setEmployee(employee);
        attendance.setAttendanceDate(today);
        attendance.setCheckInTime(LocalTime.now());
        attendance.setStatus(EAttendanceStatus.PRESENT);
        attendance.setMarkedBy(employee);

        attendanceRepository.save(attendance);

        return ResponseEntity.ok(new MessageResponse("Checked in successfully!"));
    }

    @PostMapping("/checkout")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> checkOut(Authentication authentication) {
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Optional<User> employeeOpt = userRepository.findById(userDetails.getId());
        
        if (!employeeOpt.isPresent() || employeeOpt.get().getEmployeeId() == null) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Employee profile not found!"));
        }

        User employee = employeeOpt.get();
        LocalDate today = LocalDate.now();

        // Find today's attendance record
        Optional<Attendance> attendanceOpt = attendanceRepository.findByEmployeeAndAttendanceDate(employee, today);
        if (!attendanceOpt.isPresent()) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: No check-in record found for today!"));
        }

        Attendance attendance = attendanceOpt.get();
        if (attendance.getCheckOutTime() != null) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Already checked out today!"));
        }

        attendance.setCheckOutTime(LocalTime.now());
        attendanceRepository.save(attendance);

        return ResponseEntity.ok(new MessageResponse("Checked out successfully!"));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> updateAttendance(@PathVariable Long id, 
                                            @Valid @RequestBody AttendanceRequest attendanceRequest,
                                            Authentication authentication) {
        Optional<Attendance> attendanceOpt = attendanceRepository.findById(id);
        if (!attendanceOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }

        Attendance attendance = attendanceOpt.get();
        
        // Update attendance details
        attendance.setCheckInTime(attendanceRequest.getCheckInTime());
        attendance.setCheckOutTime(attendanceRequest.getCheckOutTime());
        attendance.setStatus(attendanceRequest.getStatus());
        attendance.setRemarks(attendanceRequest.getRemarks());

        // Update marked by
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Optional<User> markedByOpt = userRepository.findById(userDetails.getId());
        if (markedByOpt.isPresent()) {
            attendance.setMarkedBy(markedByOpt.get());
        }

        attendanceRepository.save(attendance);

        return ResponseEntity.ok(new MessageResponse("Attendance updated successfully!"));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteAttendance(@PathVariable Long id) {
        if (!attendanceRepository.existsById(id)) {
            return ResponseEntity.notFound().build();
        }

        attendanceRepository.deleteById(id);
        return ResponseEntity.ok(new MessageResponse("Attendance record deleted successfully!"));
    }

    private AttendanceResponse convertToAttendanceResponse(Attendance attendance) {
        AttendanceResponse response = new AttendanceResponse();
        response.setId(attendance.getId());
        response.setEmployeeId(attendance.getEmployee().getId());
        response.setEmployeeFirstName(attendance.getEmployee().getFirstName());
        response.setEmployeeLastName(attendance.getEmployee().getLastName());
        response.setEmployeeUsername(attendance.getEmployee().getUsername());
        response.setEmployeeIdCode(attendance.getEmployee().getEmployeeId());
        response.setAttendanceDate(attendance.getAttendanceDate());
        response.setCheckInTime(attendance.getCheckInTime());
        response.setCheckOutTime(attendance.getCheckOutTime());
        response.setStatus(attendance.getStatus());
        response.setRemarks(attendance.getRemarks());
        response.setTotalHours(attendance.getTotalHours());
        response.setOvertimeHours(attendance.getOvertimeHours());
        
        if (attendance.getMarkedBy() != null) {
            response.setMarkedByUsername(attendance.getMarkedBy().getUsername());
        }

        return response;
    }

    private AttendanceListResponse convertToAttendanceListResponse(Attendance attendance) {
        List<String> roles = attendance.getEmployee().getRoles().stream()
                .map(role -> role.getName().name())
                .collect(Collectors.toList());

        return new AttendanceListResponse(
                attendance.getEmployee().getEmployeeId(),
                attendance.getEmployee().getFirstName(),
                attendance.getEmployee().getLastName(),
                roles,
                attendance.getAttendanceDate(),
                attendance.getCheckInTime(),
                attendance.getCheckOutTime(),
                attendance.getStatus(),
                attendance.getTotalHours(),
                attendance.getRemarks()
        );
    }
}
