package com.example.stockinventorysystem.repository;

import com.example.stockinventorysystem.model.ERole;
import com.example.stockinventorysystem.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Optional<Role> findByName(ERole name);
}