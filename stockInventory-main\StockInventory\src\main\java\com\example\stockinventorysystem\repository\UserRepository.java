package com.example.stockinventorysystem.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.example.stockinventorysystem.model.User;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Boolean existsByUsername(String username);
    Boolean existsByEmail(String email);

    // Employee-specific methods
    Optional<User> findByEmployeeId(String employeeId);
    Boolean existsByEmployeeId(String employeeId);

    // Find employees (users with employee ID)
    @Query("SELECT u FROM User u WHERE u.employeeId IS NOT NULL ORDER BY u.firstName, u.lastName")
    List<User> findAllEmployees();

    // Search employees by name
    @Query("SELECT u FROM User u WHERE u.employeeId IS NOT NULL AND (LOWER(u.firstName) LIKE LOWER(CONCAT('%', :name, '%')) OR LOWER(u.lastName) LIKE LOWER(CONCAT('%', :name, '%')))")
    List<User> findEmployeesByName(String name);
}