# Database Configuration
spring.datasource.url=*******************************************************************************************************
spring.datasource.username=root
spring.datasource.password=6666
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT Configuration
app.jwtSecret=m7XJV0aSND0tFSBDwIxg2sjqOqWSRcnpvQ+DeusqXYG4v2PSIixOR2+Yg4u5vJO4EDrdr99wwU9tnrmNjARrzA==
app.jwtExpirationMs=86400000

# Server Configuration
server.port=8080

# File upload settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Thymeleaf Configuration
spring.thymeleaf.check-template-location=false

# JPA Configuration - Disable open-in-view warning
spring.jpa.open-in-view=false